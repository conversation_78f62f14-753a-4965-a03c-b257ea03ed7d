use serde::{Deserialize, Serialize};
use std::sync::Arc;
use crate::dto::dim::{DimTestItem, DimTestItemRow};

/// TestItem key for grouping and deduplication
/// Corresponds to TestItemKey in Scala
/// Used for grouping TestItem records and ensuring uniqueness
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[allow(non_snake_case)]
pub struct TestItemKey {
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub FACTORY: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub WAFER_NO_KEY: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub SITE: Option<u32>,
    pub SBIN_NUM: Option<u32>,
    pub HBIN_NUM: Option<u32>,
    pub TEST_STAGE: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    pub CONDITION_SET_STR: Arc<str>,
}

impl TestItemKey {
    /// Creates a TestItemKey from DimTestItem
    /// Used for grouping DimTestItem records
    pub fn from_dim_test_item(item: &DimTestItem) -> Self {
        // Convert CONDITION_SET to string representation
        let condition_set_str = if item.CONDITION_SET.is_empty() {
            Arc::from("")
        } else {
            let condition_str = item
                .CONDITION_SET
                .iter()
                .map(|(k, v)| format!("{}={}", k, v))
                .collect::<Vec<_>>()
                .join(",");
            Arc::from(condition_str)
        };

        Self {
            CUSTOMER: item.CUSTOMER.clone(),
            SUB_CUSTOMER: item.SUB_CUSTOMER.clone(),
            TEST_AREA: item.TEST_AREA.clone(),
            FACTORY: item.FACTORY.clone(),
            DEVICE_ID: item.DEVICE_ID.clone(),
            TEST_ITEM: item.TEST_ITEM.clone(),
            LOT_ID: item.LOT_ID.clone(),
            LOT_TYPE: item.LOT_TYPE.clone(),
            WAFER_NO_KEY: item.WAFER_NO_KEY.clone(),
            SBLOT_ID: item.SBLOT_ID.clone(),
            SITE: item.SITE,
            SBIN_NUM: item.SBIN_NUM,
            HBIN_NUM: item.HBIN_NUM,
            TEST_STAGE: item.TEST_STAGE.clone(),
            TEST_PROGRAM: item.TEST_PROGRAM.clone(),
            TEST_TEMPERATURE: item.TEST_TEMPERATURE.clone(),
            TESTER_NAME: item.TESTER_NAME.clone(),
            PROBER_HANDLER_ID: item.PROBER_HANDLER_ID.clone(),
            PROBECARD_LOADBOARD_ID: item.PROBECARD_LOADBOARD_ID.clone(),
            CONDITION_SET_STR: condition_set_str,
        }
    }
}
