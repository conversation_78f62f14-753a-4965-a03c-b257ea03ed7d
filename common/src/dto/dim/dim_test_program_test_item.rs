use crate::dto::dim::{DimTestItem, TestProgramTestItemKey};
use crate::model::constant::{DIM_TEST_PROGRAM_VERSION_MAX, EMPTY};
use crate::parquet::RecordBatchWrapper;
use crate::utils::date;
use arrow::array::RecordBatch;
use arrow::datatypes::{Field, FieldRef};
use chrono::Utc;
use serde::{Deserialize, Serialize};
use serde_arrow::{schema::SchemaLike, schema::TracingOptions};
use std::sync::Arc;

#[derive(Debug, Clone, Serialize, Deserialize)]
#[allow(non_snake_case)]
pub struct DimTestProgramTestItem {
    // Customer and upload information
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: u32,

    // Factory and location information
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,

    // Test stage information
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,

    // Test program information
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,

    // Test item information
    pub TEST_NUM: Option<u32>,
    pub TEST_NUM_KEY: Arc<str>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub TESTITEM_TYPE: Arc<str>,

    // Original limits
    pub ORIGIN_HI_LIMIT: Option<f64>,
    pub ORIGIN_HI_LIMIT_KEY: Arc<str>,
    pub ORIGIN_LO_LIMIT: Option<f64>,
    pub ORIGIN_LO_LIMIT_KEY: Arc<str>,
    pub ORIGIN_UNITS: Arc<str>,

    // Processed limits
    pub LO_LIMIT: Option<f64>,
    pub LO_LIMIT_KEY: Arc<str>,
    pub HI_LIMIT: Option<f64>,
    pub HI_LIMIT_KEY: Arc<str>,
    pub UNITS: Arc<str>,

    // Test conditions
    pub CONDITION_SET: Vec<(Arc<str>, Arc<str>)>,
    pub CONDITION_SET_STR: Arc<str>,

    // System fields
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    pub CREATE_TIME: i64, // Timestamp in milliseconds
    pub CREATE_USER: Arc<str>,
    pub UPLOAD_TIME: i64, // Timestamp in milliseconds
    pub VERSION: u32,
    pub IS_DELETE: u8,
}

impl DimTestProgramTestItem {
    /// Creates a DimTestProgramTestItem from DimTestItem
    /// This implements the rich domain model pattern where business logic is in the domain model
    pub fn from_dim_test_item(test_item: &DimTestItem) -> Self {
        Self {
            CUSTOMER: test_item.CUSTOMER.clone(),
            SUB_CUSTOMER: test_item.SUB_CUSTOMER.clone(),
            UPLOAD_TYPE: test_item.UPLOAD_TYPE.clone(),
            FILE_ID: test_item.FILE_ID,
            FACTORY: test_item.FACTORY.clone(),
            FACTORY_SITE: test_item.FACTORY_SITE.clone(),
            FAB: test_item.FAB.clone(),
            FAB_SITE: test_item.FAB_SITE.clone(),
            TEST_AREA: test_item.TEST_AREA.clone(),
            TEST_STAGE: test_item.TEST_STAGE.clone(),
            DEVICE_ID: test_item.DEVICE_ID.clone(),
            TEST_PROGRAM: test_item.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: test_item.TEST_PROGRAM_VERSION.clone(),
            TEST_TEMPERATURE: test_item.TEST_TEMPERATURE.clone(),
            TEST_NUM: test_item.TEST_NUM,
            TEST_NUM_KEY: test_item.TEST_NUM.map_or_else(|| "".into(), |v| v.to_string().into()),
            TEST_TXT: test_item.TEST_TXT.clone(),
            TEST_ITEM: test_item.TEST_ITEM.clone(),
            TESTITEM_TYPE: test_item.TESTITEM_TYPE.clone(),
            ORIGIN_HI_LIMIT: test_item.ORIGIN_HI_LIMIT,
            ORIGIN_HI_LIMIT_KEY: test_item.ORIGIN_HI_LIMIT.map_or_else(|| "".into(), |v| v.to_string().into()),
            ORIGIN_LO_LIMIT: test_item.ORIGIN_LO_LIMIT,
            ORIGIN_LO_LIMIT_KEY: test_item.ORIGIN_LO_LIMIT.map_or_else(|| "".into(), |v| v.to_string().into()),
            ORIGIN_UNITS: test_item.ORIGIN_UNITS.clone(),
            LO_LIMIT: test_item.LO_LIMIT,
            LO_LIMIT_KEY: test_item.LO_LIMIT.map_or_else(|| "".into(), |v| v.to_string().into()),
            HI_LIMIT: test_item.HI_LIMIT,
            HI_LIMIT_KEY: test_item.HI_LIMIT.map_or_else(|| "".into(), |v| v.to_string().into()),
            UNITS: test_item.UNITS.clone(),
            CONDITION_SET: test_item.CONDITION_SET.clone(),
            // 这些字段在聚合以后统一赋值提高性能
            CONDITION_SET_STR: Arc::from(EMPTY),
            CREATE_HOUR_KEY: Arc::from(EMPTY),
            CREATE_DAY_KEY: Arc::from(EMPTY),
            CREATE_TIME: 0,
            CREATE_USER: test_item.CREATE_USER.clone(),
            UPLOAD_TIME: test_item.UPLOAD_TIME,
            VERSION: (DIM_TEST_PROGRAM_VERSION_MAX - test_item.START_TIME.unwrap_or(0) / 1000) as u32,
            IS_DELETE: test_item.IS_DELETE,
        }
    }

    /// Creates a TestProgramTestItemKey from this DimTestProgramTestItem
    /// Used for grouping and deduplication
    pub fn to_key(&self) -> TestProgramTestItemKey {
        TestProgramTestItemKey {
            CUSTOMER: self.CUSTOMER.clone(),
            TEST_STAGE: self.TEST_STAGE.clone(),
            TEST_PROGRAM: self.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: self.TEST_PROGRAM_VERSION.clone(),
            TEST_TEMPERATURE: self.TEST_TEMPERATURE.clone(),
            TEST_NUM_KEY: self.TEST_NUM_KEY.clone(),
            TEST_TXT: self.TEST_TXT.clone(),
            TEST_ITEM: self.TEST_ITEM.clone(),
            TESTITEM_TYPE: self.TESTITEM_TYPE.clone(),
            ORIGIN_HI_LIMIT_KEY: self.ORIGIN_HI_LIMIT_KEY.clone(),
            ORIGIN_LO_LIMIT_KEY: self.ORIGIN_LO_LIMIT_KEY.clone(),
            ORIGIN_UNITS: self.ORIGIN_UNITS.clone(),
            LO_LIMIT_KEY: self.LO_LIMIT_KEY.clone(),
            HI_LIMIT_KEY: self.HI_LIMIT_KEY.clone(),
            UNITS: self.UNITS.clone(),
            CONDITION_SET_STR: self.CONDITION_SET_STR.clone(),
        }
    }
}

impl RecordBatchWrapper for DimTestProgramTestItem {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized,
    {
        let result: Vec<DimTestProgramTestItem> = serde_arrow::from_record_batch(batch)
            .map_err(|e| format!("Failed to deserialize from RecordBatch: {}", e))?;
        Ok(result)
    }

    fn to_record_batch(data: &[Self]) -> Result<RecordBatch, String>
    where
        Self: Sized,
    {
        if data.is_empty() {
            return Err("Cannot create RecordBatch from empty data".to_string());
        }

        // Use DIM layer field processing logic
        let fields: Vec<Arc<Field>> = Vec::<FieldRef>::from_type::<DimTestProgramTestItem>(
            TracingOptions::default()
                .allow_null_fields(true)
                .map_as_struct(false)
                .strings_as_large_utf8(false),
        )
        .map_err(|e| format!("Failed to create fields from type: {}", e))?;

        let record_batch =
            serde_arrow::to_record_batch(&fields, &data).map_err(|e| format!("Failed to create RecordBatch: {}", e))?;

        Ok(record_batch)
    }
}

impl Default for DimTestProgramTestItem {
    fn default() -> Self {
        use crate::model::constant::EMPTY;

        Self {
            CUSTOMER: Arc::from(EMPTY),
            SUB_CUSTOMER: Arc::from(EMPTY),
            UPLOAD_TYPE: Arc::from(EMPTY),
            FILE_ID: 0,
            FACTORY: Arc::from(EMPTY),
            FACTORY_SITE: Arc::from(EMPTY),
            FAB: Arc::from(EMPTY),
            FAB_SITE: Arc::from(EMPTY),
            TEST_AREA: Arc::from(EMPTY),
            TEST_STAGE: Arc::from(EMPTY),
            DEVICE_ID: Arc::from(EMPTY),
            TEST_PROGRAM: Arc::from(EMPTY),
            TEST_PROGRAM_VERSION: Arc::from(EMPTY),
            TEST_TEMPERATURE: Arc::from(EMPTY),
            TEST_NUM: None,
            TEST_NUM_KEY: Arc::from(EMPTY),
            TEST_TXT: Arc::from(EMPTY),
            TEST_ITEM: Arc::from(EMPTY),
            TESTITEM_TYPE: Arc::from(EMPTY),
            ORIGIN_HI_LIMIT: None,
            ORIGIN_HI_LIMIT_KEY: Arc::from(EMPTY),
            ORIGIN_LO_LIMIT: None,
            ORIGIN_LO_LIMIT_KEY: Arc::from(EMPTY),
            ORIGIN_UNITS: Arc::from(EMPTY),
            LO_LIMIT: None,
            LO_LIMIT_KEY: Arc::from(EMPTY),
            HI_LIMIT: None,
            HI_LIMIT_KEY: Arc::from(EMPTY),
            UNITS: Arc::from(EMPTY),
            CONDITION_SET: Vec::new(),
            CONDITION_SET_STR: Arc::from(EMPTY),
            CREATE_HOUR_KEY: Arc::from(EMPTY),
            CREATE_DAY_KEY: Arc::from(EMPTY),
            CREATE_TIME: 0,
            CREATE_USER: Arc::from(EMPTY),
            UPLOAD_TIME: 0,
            VERSION: 0,
            IS_DELETE: 0,
        }
    }
}
