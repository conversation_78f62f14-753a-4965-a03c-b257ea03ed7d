use crate::dto::dim::DimTestProgramTestItem;
use crate::utils::decimal::Decimal38_18;
use chrono::{DateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;

/// DIM TestProgramTestItem dimension table structure
/// Contains test program and test item dimension data
/// Corresponds to dim_test_program_test_item_local table
#[derive(Debug, Clone, Serialize, Deserialize, Row, PartialEq)]
#[allow(non_snake_case)]
pub struct DimTestProgramTestItemRow {
    // Customer and upload information
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: u32,

    // Factory and location information
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,

    // Test stage information
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,

    // Test program information
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,

    // Test item information
    pub TEST_NUM: Option<u32>,
    pub TEST_NUM_KEY: Arc<str>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub TESTITEM_TYPE: Arc<str>,

    // Original limits
    pub ORIGIN_HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_HI_LIMIT_KEY: Arc<str>,
    pub ORIGIN_LO_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_LO_LIMIT_KEY: Arc<str>,
    pub ORIGIN_UNITS: Arc<str>,

    // Processed limits
    pub LO_LIMIT: Option<Decimal38_18>,
    pub LO_LIMIT_KEY: Arc<str>,
    pub HI_LIMIT: Option<Decimal38_18>,
    pub HI_LIMIT_KEY: Arc<str>,
    pub UNITS: Arc<str>,

    // Test conditions
    pub CONDITION_SET: Vec<(Arc<str>, Arc<str>)>,
    pub CONDITION_SET_STR: Arc<str>,

    // System fields
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_USER: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub VERSION: u32,
    pub IS_DELETE: u8,
}

impl DimTestProgramTestItemRow {
    /// Creates a new DimTestProgramTestItemRow with the provided values
    pub fn new(
        customer: &str,
        sub_customer: &str,
        upload_type: &str,
        file_id: u32,
        factory: &str,
        factory_site: &str,
        fab: &str,
        fab_site: &str,
        test_area: &str,
        test_stage: &str,
        device_id: &str,
        test_program: &str,
        test_program_version: &str,
        test_temperature: &str,
        test_num: Option<u32>,
        test_txt: &str,
        test_item: &str,
        testitem_type: &str,
        origin_hi_limit: Option<Decimal38_18>,
        origin_lo_limit: Option<Decimal38_18>,
        origin_units: &str,
        lo_limit: Option<Decimal38_18>,
        hi_limit: Option<Decimal38_18>,
        units: &str,
        condition_set: &HashMap<String, String>,
        create_time: DateTime<Utc>,
        create_user: &str,
        upload_time: DateTime<Utc>,
        version: u32,
    ) -> Self {
        use crate::utils::date::{get_day, get_day_hour};

        let create_hour_key = get_day_hour(create_time);
        let create_day_key = get_day(create_time);

        // Convert TEST_NUM to string key
        let test_num_key = test_num.map_or_else(|| "".to_string(), |n| n.to_string());

        // Convert limits to string keys
        let origin_hi_limit_key = origin_hi_limit.map_or_else(|| "".to_string(), |l| l.to_string());
        let origin_lo_limit_key = origin_lo_limit.map_or_else(|| "".to_string(), |l| l.to_string());
        let lo_limit_key = lo_limit.map_or_else(|| "".to_string(), |l| l.to_string());
        let hi_limit_key = hi_limit.map_or_else(|| "".to_string(), |l| l.to_string());

        // Convert condition set to sorted string and vector
        let mut condition_pairs: Vec<(Arc<str>, Arc<str>)> = condition_set
            .iter()
            .map(|(k, v)| (Arc::from(k.as_str()), Arc::from(v.as_str())))
            .collect::<Vec<_>>();
        condition_pairs.sort_by(|a, b| a.0.cmp(&b.0));

        let condition_set_str = condition_pairs
            .iter()
            .map(|(k, v)| format!("{}:{}", k, v))
            .collect::<Vec<_>>()
            .join(",");

        Self {
            CUSTOMER: Arc::from(customer),
            SUB_CUSTOMER: Arc::from(sub_customer),
            UPLOAD_TYPE: Arc::from(upload_type),
            FILE_ID: file_id,
            FACTORY: Arc::from(factory),
            FACTORY_SITE: Arc::from(factory_site),
            FAB: Arc::from(fab),
            FAB_SITE: Arc::from(fab_site),
            TEST_AREA: Arc::from(test_area),
            TEST_STAGE: Arc::from(test_stage),
            DEVICE_ID: Arc::from(device_id),
            TEST_PROGRAM: Arc::from(test_program),
            TEST_PROGRAM_VERSION: Arc::from(test_program_version),
            TEST_TEMPERATURE: Arc::from(test_temperature),
            TEST_NUM: test_num,
            TEST_NUM_KEY: Arc::from(test_num_key.as_str()),
            TEST_TXT: Arc::from(test_txt),
            TEST_ITEM: Arc::from(test_item),
            TESTITEM_TYPE: Arc::from(testitem_type),
            ORIGIN_HI_LIMIT: origin_hi_limit,
            ORIGIN_HI_LIMIT_KEY: Arc::from(origin_hi_limit_key.as_str()),
            ORIGIN_LO_LIMIT: origin_lo_limit,
            ORIGIN_LO_LIMIT_KEY: Arc::from(origin_lo_limit_key.as_str()),
            ORIGIN_UNITS: Arc::from(origin_units),
            LO_LIMIT: lo_limit,
            LO_LIMIT_KEY: Arc::from(lo_limit_key.as_str()),
            HI_LIMIT: hi_limit,
            HI_LIMIT_KEY: Arc::from(hi_limit_key.as_str()),
            UNITS: Arc::from(units),
            CONDITION_SET: condition_pairs,
            CONDITION_SET_STR: Arc::from(condition_set_str.as_str()),
            CREATE_HOUR_KEY: Arc::from(create_hour_key.as_str()),
            CREATE_DAY_KEY: Arc::from(create_day_key.as_str()),
            CREATE_TIME: create_time,
            CREATE_USER: Arc::from(create_user),
            UPLOAD_TIME: upload_time,
            VERSION: version,
            IS_DELETE: 0,
        }
    }

    /// Convert DimTestProgramTestItem to DimTestProgramTestItemRow
    /// This implements the rich domain model pattern where conversion logic is in the Row model
    pub fn from_dim_test_program_test_item(item: &DimTestProgramTestItem) -> Self {
        // Helper function to convert Option<f64> to Option<Decimal38_18>
        let to_decimal =
            |val: Option<f64>| -> Option<Decimal38_18> { val.and_then(|v| Decimal38_18::try_from(v).ok()) };

        // Convert timestamps to DateTime<Utc>
        let create_time = chrono::DateTime::from_timestamp_millis(item.CREATE_TIME)
            .unwrap_or_else(|| chrono::Utc::now());
        let upload_time = chrono::DateTime::from_timestamp_millis(item.UPLOAD_TIME)
            .unwrap_or_else(|| chrono::Utc::now());

        Self {
            CUSTOMER: item.CUSTOMER.clone(),
            SUB_CUSTOMER: item.SUB_CUSTOMER.clone(),
            UPLOAD_TYPE: item.UPLOAD_TYPE.clone(),
            FILE_ID: item.FILE_ID,
            FACTORY: item.FACTORY.clone(),
            FACTORY_SITE: item.FACTORY_SITE.clone(),
            FAB: item.FAB.clone(),
            FAB_SITE: item.FAB_SITE.clone(),
            TEST_AREA: item.TEST_AREA.clone(),
            TEST_STAGE: item.TEST_STAGE.clone(),
            DEVICE_ID: item.DEVICE_ID.clone(),
            TEST_PROGRAM: item.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: item.TEST_PROGRAM_VERSION.clone(),
            TEST_TEMPERATURE: item.TEST_TEMPERATURE.clone(),
            TEST_NUM: item.TEST_NUM,
            TEST_NUM_KEY: item.TEST_NUM_KEY.clone(),
            TEST_TXT: item.TEST_TXT.clone(),
            TEST_ITEM: item.TEST_ITEM.clone(),
            TESTITEM_TYPE: item.TESTITEM_TYPE.clone(),
            ORIGIN_HI_LIMIT: to_decimal(item.ORIGIN_HI_LIMIT),
            ORIGIN_HI_LIMIT_KEY: item.ORIGIN_HI_LIMIT_KEY.clone(),
            ORIGIN_LO_LIMIT: to_decimal(item.ORIGIN_LO_LIMIT),
            ORIGIN_LO_LIMIT_KEY: item.ORIGIN_LO_LIMIT_KEY.clone(),
            ORIGIN_UNITS: item.ORIGIN_UNITS.clone(),
            LO_LIMIT: to_decimal(item.LO_LIMIT),
            LO_LIMIT_KEY: item.LO_LIMIT_KEY.clone(),
            HI_LIMIT: to_decimal(item.HI_LIMIT),
            HI_LIMIT_KEY: item.HI_LIMIT_KEY.clone(),
            UNITS: item.UNITS.clone(),
            CONDITION_SET: item.CONDITION_SET.clone(),
            CONDITION_SET_STR: item.CONDITION_SET_STR.clone(),
            CREATE_HOUR_KEY: item.CREATE_HOUR_KEY.clone(),
            CREATE_DAY_KEY: item.CREATE_DAY_KEY.clone(),
            CREATE_TIME: create_time,
            CREATE_USER: item.CREATE_USER.clone(),
            UPLOAD_TIME: upload_time,
            VERSION: item.VERSION,
            IS_DELETE: item.IS_DELETE,
        }
    }
}
