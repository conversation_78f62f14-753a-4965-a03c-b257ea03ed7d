use crate::config::DwTestItemConfig;
use crate::service::dim::test_item_service::TestItemService;
use crate::service::dim::test_program_test_item_service::TestProgramTestItemService;
use crate::service::dim::{
    get_test_program_partition, write_to_ck_by_partition, write_to_clickhouse_after_tombstone, write_to_hdfs,
};
use common::ck::ck_sink;
use common::ck::ck_sink::CkSink;
use common::dim::sink::{TestItemHandler, TestProgramTestItemHandler};
use common::dto::dim::{DimTestItem, DimTestItemRow, DimTestProgramTestItem, DimTestProgramTestItemRow};
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dws::model::SiteTestItemIndex;
use common::model::dw_table_enum::DwTableEnum;
use common::model::key::Wafer<PERSON>ey;
use common::parquet::RecordBatchWrapper;
use common::utils::path;
use parquet_provider::parquet_provider::{write_parquet, write_parquet_multi};
use std::collections::HashMap;
use std::error::Error;

/// FtDimTestItemService handles FT (Final Test) stage DIM layer processing
/// Corresponds to FtDimTestItemService.scala
#[derive(Debug, Clone)]
pub struct FtDimTestItemService {
    properties: DwTestItemConfig,
}

impl FtDimTestItemService {
    pub fn new(properties: DwTestItemConfig) -> Self {
        Self { properties }
    }

    /// Calculate FT DIM layer data
    /// Corresponds to calculate method in FtDimTestItemService.scala
    pub async fn calculate(
        &self,
        sub_test_item: &Vec<Vec<SubTestItemDetail>>,
        file_detail_map: &HashMap<i64, FileDetail>,
        wafer_key: WaferKey,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        log::info!("开始计算 FT DIM 层数据...");

        let dim_db_name = &self.properties.dim_db_name;

        // 1. 计算 DIM TestItem
        log::info!("开始计算 DIM TestItem...");
        let test_item_service = TestItemService::new(self.properties.clone());
        let test_items = test_item_service.calculate_test_item(sub_test_item, file_detail_map, false).await?;
        log::info!("DIM TestItem 计算完成，共生成 {} 条记录", test_items.len());

        // Convert DimTestItem to DimTestItemRow for ClickHouse storage
        let test_item_rows: Vec<DimTestItemRow> =
            test_items.iter().map(|item| DimTestItemRow::from_dim_test_item(item)).collect();

        // Write DimTestItem to HDFS (Parquet)
        FtDimTestItemService::write_test_items_to_hdfs(&self.properties, &test_items, &wafer_key).await?;

        // Write DimTestItem to ClickHouse
        let test_item_handler = TestItemHandler::new(dim_db_name.to_string(), self.properties.insert_cluster_table);
        write_to_clickhouse_after_tombstone(test_item_handler, &self.properties, &test_item_rows, &wafer_key).await?;

        log::info!("开始计算 DIM TestProgramTestItem...");
        let test_program_test_item_service = TestProgramTestItemService::new(self.properties.clone());
        let test_program_test_item = test_program_test_item_service
            .calculate_test_program_test_item_from_test_item(&test_items)
            .await?;
        log::info!("DIM TestProgramTestItem 计算完成");

        // Write DimTestProgramTestItem to HDFS (Parquet)
        FtDimTestItemService::write_test_program_test_items_to_hdfs(&self.properties, &test_program_test_item, &wafer_key).await?;

        // Convert DimTestProgramTestItem to DimTestProgramTestItemRow for ClickHouse storage
        let test_program_test_item_rows: Vec<DimTestProgramTestItemRow> = test_program_test_item
            .iter()
            .map(|item| DimTestProgramTestItemRow::from_dim_test_program_test_item(item))
            .collect();

        // Write DimTestProgramTestItem to ClickHouse
        let test_program_test_item_handler = TestProgramTestItemHandler::new(dim_db_name.to_string());
        let partition = get_test_program_partition(
            wafer_key.customer.as_str(),
            wafer_key.test_area.as_str(),
            wafer_key.factory.as_str(),
        );
        write_to_ck_by_partition(
            &self.properties,
            &test_program_test_item_rows,
            test_program_test_item_handler,
            partition.as_str(),
        )
        .await?;
        log::info!("DIM TestProgramTestItem 写入clickhouse完成");
        log::info!("FT DIM 层数据计算完成");
        Ok(())
    }

    /// Write DimTestItem data to HDFS using Parquet format
    async fn write_test_items_to_hdfs(
        config: &DwTestItemConfig,
        data: &[DimTestItem],
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        if data.is_empty() {
            log::warn!("No DimTestItem data to write to HDFS");
            return Ok(());
        }

        let table_path = path::get_lot_path(
            &config.ft_dim_result_dir_template,
            DwTableEnum::DimTestItem.get_dir_table_name().as_str(),
            &wafer_key.test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );

        log::info!("写入DimTestItem parquet文件到路径: {}", table_path);

        write_to_hdfs(table_path.as_str(), data, config.get_batch_size()?,&config.get_hdfs_config()).await?;
        log::info!("成功写入DimTestItem parquet文件到路径: {}", table_path);

        Ok(())
    }

    /// Write DimTestProgramTestItem data to HDFS using Parquet format
    async fn write_test_program_test_items_to_hdfs(
        config: &DwTestItemConfig,
        data: &[DimTestProgramTestItem],
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        if data.is_empty() {
            log::warn!("No DimTestProgramTestItem data to write to HDFS");
            return Ok(());
        }

        let table_path = path::get_lot_path(
            &config.ft_dim_result_dir_template,
            DwTableEnum::DimTestProgramTestItem.get_dir_table_name().as_str(),
            &wafer_key.test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );

        log::info!("写入DimTestProgramTestItem parquet文件到路径: {}", table_path);
        write_to_hdfs(table_path.as_str(), data, config.get_batch_size()?,&config.get_hdfs_config()).await?;
        log::info!("成功写入DimTestProgramTestItem parquet文件到路径: {}", table_path);

        Ok(())
    }
}
