use std::sync::Arc;
use chrono::Utc;
use dashmap::DashMap;
use rayon::prelude::*;

use crate::config::DwTestItemConfig;
use common::dto::dim::{
    DimTestItem, DimTestProgramTestItem, DimTestProgramTestItemRow, TestProgramTestItemKey,
};
use common::utils::date;

/// TestProgramTestItemService handles DIM layer DimTestProgramTestItem processing
/// Corresponds to TestProgramTestItemService.scala
#[derive(Debug, Clone)]
pub struct TestProgramTestItemService {
    properties: DwTestItemConfig,
}

impl TestProgramTestItemService {
    pub fn new(properties: DwTestItemConfig) -> Self {
        Self { properties }
    }

    /// Group DimTestProgramTestItem by key and take max version for deduplication
    /// Corresponds to groupByKey and mapGroups logic in Scala
    /// Optimized version using DashMap for concurrent grouping
    fn group_max_version(&self, items: Vec<DimTestProgramTestItem>) -> Vec<DimTestProgramTestItem> {
        log::info!("开始分组去重，共 {} 条记录", items.len());

        // Use DashMap for concurrent grouping - better performance than HashMap
        let grouped: DashMap<TestProgramTestItemKey, Vec<DimTestProgramTestItem>> = DashMap::new();

        // Parallel grouping using rayon - each thread can safely access DashMap
        items.into_par_iter().for_each(|item| {
            let key = item.to_key();
            grouped.entry(key).or_insert_with(Vec::new).push(item);
        });

        // Take max version from each group (optimized: O(n) with parallel processing)
        // Convert DashMap to Vec for parallel processing
        let groups: Vec<_> = grouped.into_iter().collect();
        let now = Utc::now();
        let now_timestamp: i64 = now.timestamp_millis();
        let create_day_key: Arc<str> = Arc::from(date::get_day(now));
        let create_hour_key: Arc<str> = Arc::from(date::get_day_hour(now));
        let result: Vec<DimTestProgramTestItem> = groups
            .into_par_iter()
            .filter_map(|(key, group)| {
                // Find the item with maximum VERSION directly without sorting
                // Uses parallel iterator for better performance on large datasets
                let max_item = group.into_iter().max_by_key(|item| item.VERSION);
                max_item.map(|mut item| {
                    item.CONDITION_SET_STR = key.CONDITION_SET_STR.clone();
                    item.CREATE_HOUR_KEY = create_hour_key.clone();
                    item.CREATE_DAY_KEY = create_day_key.clone();
                    item.CREATE_TIME = now_timestamp;
                    item
                })
            })
            .collect();

        log::info!("分组去重完成，结果 {} 条记录", result.len());
        result
    }

    /// Calculate DimTestProgramTestItem from DimTestItem (new method for DIM layer)
    /// Corresponds to calculateTestProgramTestItem method in Scala that takes TestItem as input
    pub async fn calculate_test_program_test_item_from_test_item(
        &self,
        test_items: &Vec<DimTestItem>,
    ) -> Result<Vec<DimTestProgramTestItem>, Box<dyn std::error::Error + Send + Sync>> {
        log::info!("开始从 DimTestItem 计算 DIM DimTestProgramTestItem...");

        // Transform DimTestItem to DimTestProgramTestItem using rich domain model
        let test_program_test_items: Vec<DimTestProgramTestItem> = test_items
            .par_iter()
            .map(|test_item| DimTestProgramTestItem::from_dim_test_item(test_item))
            .collect();

        // Group by key and take max version (deduplication)
        let grouped_items = self.group_max_version(test_program_test_items);

        let result: Vec<DimTestProgramTestItem> = grouped_items;

        let result_count = result.len();
        log::info!("从 DimTestItem 计算 DIM DimTestProgramTestItem 完成，共处理 {} 条记录", result_count);

        Ok(result)
    }
}
